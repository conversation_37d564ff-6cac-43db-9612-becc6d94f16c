from ninja import Query, Router
from ninja.errors import HttpError
from infra.filter import filter
from infra.PageNumberPagination import PageNumberPagination
from infra.decorators import permission_required
from lca.operation_log.decorators import log_operation
from lca.operation_log.models import OperationType
from lca.users.models import (
    USER_CERTIFICATION_STATUS,
    USER_TYPES,
    Group,
    User,
    UserCertification,
)
from lca.users.schema import (
    CreateUserSchema,
    GroupDetailSchema,
    GroupInSchema,
    GroupWithCountSchema,
    PermissionSchema,
    UserCertificateRejectInSchema,
    UserCertificationSchema,
    UserMySchema,
    UserRowMoreSchema,
)
from ninja.pagination import paginate
from lca.users.services import UserOperationService, UserService
from django.db.models import Count

router = Router(tags=["后台-用户"])


@router.get(
    "",
    summary="获取用户列表",
    response=list[UserRowMoreSchema],
    operation_id="admin_get_user_list",
)
@paginate(PageNumberPagination)
@permission_required("SystemUser")
def get_user_list(
    request,
    group_id: str = Query(None, title="角色ID"),
    is_active: bool = Query(None, title="是否有效"),
    cerificate_status: USER_CERTIFICATION_STATUS = Query(None, title="认证状态"),
):
    is_staff = None
    if cerificate_status:
        is_staff = False

    return filter(
        User.objects.order_by("-is_superuser", "-id").all(),
        groups=group_id,
        is_staff=is_staff,
        is_active=is_active,
        certificate_status=cerificate_status,
    )


@router.post("", summary="创建用户", response=UserMySchema, operation_id="admin_create_user")
@permission_required("System:SystemUser:Add")
@log_operation(OperationType.CREATE, main_content="用户", content_field="nickname")
def create_user(request, data: CreateUserSchema):
    return UserService.create_user(data)


@router.post(
    "/{id}/update",
    summary="更新用户",
    response=UserMySchema,
    operation_id="admin_update_user",
)
@permission_required("System:SystemUser:edit")
@log_operation(OperationType.EDIT, main_content="用户", content_field="nickname")
def update_user(request, id: int, data: CreateUserSchema):
    user = User.objects.get(pk=id)
    return UserService.update_user(user, data)


@router.get(
    "/group",
    summary="获取角色列表",
    response=list[GroupWithCountSchema],
    operation_id="admin_get_user_groups",
)
@permission_required("SystemRole")
def get_user_groups(request):
    return Group.objects.annotate(user_count=Count("users")).order_by("-is_predefined", "id").all()


@router.post(
    "/group",
    summary="创建角色",
    response=GroupDetailSchema,
    operation_id="admin_create_user_group",
)
@permission_required("System:SystemRole:Add")
@log_operation(OperationType.CREATE, main_content="角色", content_field="name")
def create_user_group(request, data: GroupInSchema):
    return UserService.create_group(data, request.user)


@router.get(
    "/group/{id}",
    summary="获取角色详情",
    response=GroupDetailSchema,
    operation_id="admin_get_user_group_detail",
)
@permission_required("SystemRole")
def get_user_group_detail(request, id: int):
    return Group.objects.get(pk=id)


@router.post(
    "/group/{id}/update",
    summary="更新角色",
    response=GroupDetailSchema,
    operation_id="admin_update_user_group",
)
@permission_required("System:SystemRole:edit")
@log_operation(OperationType.EDIT, main_content="角色", content_field="name")
def update_user_group(request, id: int, data: GroupInSchema):
    return UserService.update_group(Group.objects.get(pk=id), data)


@router.post(
    "/group/{id}/permissions",
    summary="更新角色权限",
    response=GroupDetailSchema,
    operation_id="admin_update_user_group_permissions",
)
@permission_required("System:SystemRole:PSet")
@log_operation(OperationType.SET_PERMISSIONS, operation_func=UserOperationService.update_group_permissions_log)
def update_user_group_permissions(request, id: int, data: list[int]):
    return UserService.update_group_permissions(Group.objects.get(pk=id), data)


@router.get(
    "/permission",
    summary="获取权限列表",
    response=list[PermissionSchema],
    operation_id="admin_get_user_permissions",
)
@permission_required("SystemRole")
def get_user_permissions(request):
    return UserService.get_all_permissions()


@router.get(
    "/certification",
    summary="获取待认证信息列表",
    response=list[UserCertificationSchema],
    operation_id="admin_get_user_certification_list",
)
@paginate(PageNumberPagination)
@permission_required("SystemUserProcess")
def get_user_certification(request, type: USER_TYPES = Query(None, title="用户类型")):
    return UserService.search_current_certification(type)


@router.get(
    "/certification/logs",
    summary="获取认证日志列表",
    response=list[UserCertificationSchema],
    operation_id="admin_get_user_certification_logs",
)
@paginate(PageNumberPagination)
@permission_required("SystemUserProcess")
def get_user_certification_logs(
    request,
    type: USER_TYPES = Query(None, title="用户类型"),
    status: USER_CERTIFICATION_STATUS = Query(None, title="认证状态"),
):
    return UserService.search_certification_logs(type=type, status=status)


@router.get(
    "/certification/{id}",
    summary="获取认证信息详情",
    response=UserCertificationSchema,
    operation_id="admin_get_user_certification_detail",
)
@permission_required("SystemUserProcess")
def get_user_certification_detail(request, id: int):
    return UserCertification.objects.get(pk=id)


@router.post(
    "/certification/{id}/accept",
    summary="通过认证",
    response=UserCertificationSchema,
    operation_id="admin_accept_user_certification",
)
@permission_required("System:SystemUserProcess:Audit")
@log_operation(OperationType.REVIEW_ACCEPT, main_content="用户", content_field="user.nickname")
def accept_user_certification(request, id: int):
    certification = UserCertification.objects.get(pk=id)
    return UserService.accept_user_certification(certification, request.user)


@router.post(
    "/certification/{id}/reject",
    summary="拒绝认证",
    response=UserCertificationSchema,
    operation_id="admin_reject_user_certification",
)
@permission_required("System:SystemUserProcess:Audit")
@log_operation(OperationType.REVIEW_REJECT, main_content="用户", content_field="user.nickname")
def reject_user_certification(request, id: int, data: UserCertificateRejectInSchema):
    certification = UserCertification.objects.get(pk=id)
    return UserService.reject_user_certification(certification, data.reason, request.user)


@router.get(
    "/{id}",
    summary="获取用户详情",
    response=UserMySchema,
    operation_id="admin_get_user_detail",
)
@permission_required("System:SystemUser:Detail")
def get_user_detail(request, id: int):
    return User.objects.get(pk=id)


@router.post(
    "/{id}/status",
    summary="切换用户状态",
    response=UserMySchema,
    operation_id="admin_toggle_user_status",
)
@permission_required("System:SystemUser:Status")
@log_operation(OperationType.TOGGLE_STATUS, main_content="用户", content_field="is_active")
def toggle_user_status(request, id: int):
    user = User.objects.get(pk=id)
    if user.is_superuser:
        raise HttpError(403, "不能修改超级管理员状态")
    user.is_active = not user.is_active
    user.save()
    return user


@router.post(
    "/group/{id}/status",
    summary="切换角色状态",
    response=GroupDetailSchema,
    operation_id="admin_toggle_group_status",
)
@log_operation(OperationType.TOGGLE_STATUS, main_content="角色", content_field="is_active")
@permission_required("System:SystemRole:Status")
def toggle_group_status(request, id: int):
    group = Group.objects.get(pk=id)
    group.is_active = not group.is_active
    group.save()
    return group
