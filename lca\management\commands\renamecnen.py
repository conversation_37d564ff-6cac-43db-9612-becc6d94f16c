import json
from django.core.management import BaseCommand
from django.db import transaction
from lca.accounting.models import EmissionSource
from lca.database.models import EmissionSourceDataSet, EmissionSourceManagement, Flow


class Command(BaseCommand):
    help = "修改为中英文"

    def handle(self, *args, **options):
        run()


def run():
    with transaction.atomic():
        for emission_source in EmissionSource.objects.all():
            if hasattr(emission_source.name, "zh"):
                continue
            emission_source.name = json.dumps({
                "zh": emission_source.name,
                "en": "",
            })
            emission_source.source = json.dumps({
                "zh": emission_source.source,
                "en": "",
            })
            emission_source.description = json.dumps({
                "zh": emission_source.description,
                "en": "",
            })
            emission_source.save()
        for emission_source_dataset in EmissionSourceDataSet.objects.all():
            if hasattr(emission_source_dataset.name, "zh"):
                continue
            emission_source_dataset.name = json.dumps({
                "zh": emission_source_dataset.name,
                "en": "",
            }
            emission_source_dataset.alias = {
                "zh": emission_source_dataset.alias,
                "en": "",
            }
            emission_source_dataset.technical_description = {
                "zh": emission_source_dataset.technical_description,
                "en": "",
            }
            emission_source_dataset.usage = {
                "zh": emission_source_dataset.usage,
                "en": "",
            }
            emission_source_dataset.allocation_principles = {
                "zh": emission_source_dataset.allocation_principles,
                "en": "",
            }
            emission_source_dataset.model_description = {
                "zh": emission_source_dataset.model_description,
                "en": "",
            }
            emission_source_dataset.data_treatment = {
                "zh": emission_source_dataset.data_treatment,
                "en": "",
            }
            emission_source_dataset.functional_unit = {
                "zh": emission_source_dataset.functional_unit,
                "en": "",
            }
            emission_source_dataset.specs = {
                "zh": emission_source_dataset.specs,
                "en": "",
            }
            emission_source_dataset.save()
        for emission_source_management in EmissionSourceManagement.objects.all():
            if hasattr(emission_source_management.generate_contact, "zh"):
                continue
            emission_source_management.generate_contact = {
                "zh": emission_source_management.generate_contact,
                "en": "",
            }
            emission_source_management.inputer_contact = {
                "zh": emission_source_management.inputer_contact,
                "en": "",
            }
            emission_source_management.approve_contact = {
                "zh": emission_source_management.approve_contact,
                "en": "",
            }
            emission_source_management.owener = {
                "zh": emission_source_management.owener,
                "en": "",
            }
            emission_source_management.view = {
                "zh": emission_source_management.view,
                "en": "",
            }
            emission_source_management.copyright = {
                "zh": emission_source_management.copyright,
                "en": "",
            }
            emission_source_management.save()
        for flow in Flow.objects.all():
            if hasattr(flow.name, "zh"):
                continue
            flow.name = {
                "zh": flow.name,
                "en": "",
            }
            flow.save()
